<?php

use App\Http\Controllers\ArrivalproductController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\CommandController;
use App\Http\Controllers\CommandVariantController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ImageController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SubcategoryController;
use App\Http\Controllers\VariantController;
use App\Http\Middleware\RoleMiddleware;
use App\Models\CommandVariant;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {


    return view('auth.login');
});



// Route::middleware(['auth', 'role:admin,intern'])->group(function () { 


// });

Route::middleware(['auth', RoleMiddleware::class])->group(function () {
    Route::delete('/clients/delete/{id}', [ClientController::class, 'destroy'])->name('clients.delete');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::delete('/delete/product/{id}', [ProductController::class, 'destroy'])->name('product.delete');
    Route::delete('/delete/variant/{id}', [VariantController::class, 'destroy'])->name('variant.delete');
    Route::delete('/delete/image/{id}', [ImageController::class, 'destroy'])->name('image.delete');
    Route::delete('blogs/delete/{blog}', [BlogController::class, 'destroy'])->name('blog.delete');
    Route::delete('/categories/{category}', [CategoryController::class, 'destroy'])->name('categories.destroy');
    Route::delete('/subcategories/{subcategory}', [SubcategoryController::class, 'destroy'])->name('subcategories.destroy');
});

Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    //?Clients
    Route::get('/clients', [ClientController::class, 'index'])->name('clients.index');
    Route::get('/clients/create', [ClientController::class, 'create'])->name('clients.create');
    Route::post('/clients/store', [ClientController::class, 'store'])->name('clients.store');
    Route::get('/clients/{id}/edit', [ClientController::class, 'edit'])->name('clients.edit');
    Route::put('/clients/update/{id}', [ClientController::class, 'update'])->name('clients.update');
    Route::get('/clients/{id}/historique', [ClientController::class, 'historique'])->name('clients.historique');

    //*Products
    Route::get('/products', [ProductController::class, 'index'])->name('products.index');
    //!Commande
    Route::get('/cart', [CommandController::class, 'index'])->name('cart.index');
    Route::post('/cart/store', [ProductController::class, 'store'])->name('cart.store');
    Route::post('/checkout', [CommandController::class, 'store'])->name('checkout.store');
    Route::get('/commands', [CommandController::class, 'show'])->name('command.index');
    Route::get('/command-variants/{id}/edit', [CommandVariantController::class, 'edit'])->name('commandVariants.edit');
    Route::patch('/command-variants/{id}', [CommandVariantController::class, 'update'])->name('commandVariants.update');
    Route::get('/get-sizes/{variantId}', [CommandVariantController::class, 'getSizes']);
    Route::post('/cart/remove', [CommandController::class, 'removeFromCart'])->name('cart.remove');
    //?ArrivalProducts
    Route::get('/arrival', [ArrivalproductController::class, 'index'])->name('arrival.index');
    Route::get('/arrival/{id}/edit', [ArrivalProductController::class, 'edit'])->name('arrival.edit');
    Route::patch('/arrival/{id}', [ArrivalProductController::class, 'update'])->name('arrival.update');
    Route::delete('/delete/product/{id}', [ProductController::class, 'destroy'])->name('product.delete');
    Route::put('/update/product/{product}', [VariantController::class, 'update'])->name('product.update');
    Route::get('/product/{id}', [ProductController::class, 'show'])->name('product.show');
    Route::post('/add_product', [VariantController::class, 'store'])->name('product.store');
    Route::get('/addproduct', [VariantController::class, 'index'])->name('product.index');
    Route::get('/restock/variant/{id}', [VariantController::class, 'show'])->name('variant.show');
    Route::patch('/restock/variant/{product}', [VariantController::class, 'restock'])->name('variant.restock');
    // images
    Route::post('/upload/image', [ImageController::class, 'store'])->name('image.store');

    // Categories and Subcategories
    Route::resource('categories', CategoryController::class);
    Route::resource('subcategories', SubcategoryController::class);
});
//Contact :
Route::get('/contacts', [ContactController::class, 'index'])->name('contacts.index');
Route::get('/contacts/destroy', [ContactController::class, 'destroy'])->name('contacts.destroy');

// ^^ Blogs :
Route::get('/blogs', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blogs/create', [BlogController::class, 'create'])->name('blog.create');
Route::post('/blogs', [BlogController::class, 'store'])->name('blog.store');
// Route::get('/blogs', [BlogController::class, 'index']);
Route::post('blogs/store', [BlogController::class, 'store'])->name('blog.store');
Route::put('blogs/update/{blog}', [BlogController::class, 'update'])->name('blog.update');
Route::get('/blogs/edit/{blog}', [BlogController::class, 'edit'])->name('blog.edit');
require __DIR__ . '/auth.php';
require __DIR__ . '/auth.php';
